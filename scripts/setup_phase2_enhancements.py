#!/usr/bin/env python3
"""
Setup script for Phase 2 architecture improvements and observability features.

This script installs dependencies, configures services, and sets up
the enhanced agent framework with all new capabilities.
"""

import os
import sys
import subprocess
import json
import yaml
from pathlib import Path
from typing import Dict, List, Any


class Phase2SetupManager:
    """Manager for Phase 2 setup and configuration."""
    
    def __init__(self):
        """Initialize the setup manager."""
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.dashboards_dir = self.project_root / "dashboards"
        
    def run_setup(self) -> None:
        """Run the complete Phase 2 setup."""
        print("🚀 Setting up Agent Framework Phase 2 Enhancements...")
        
        try:
            # Install dependencies
            self._install_dependencies()
            
            # Create configuration files
            self._create_configurations()
            
            # Set up observability
            self._setup_observability()
            
            # Generate dashboards
            self._generate_dashboards()
            
            # Create example configurations
            self._create_examples()
            
            # Run validation tests
            self._run_validation()
            
            print("\n✅ Phase 2 setup completed successfully!")
            self._print_next_steps()
            
        except Exception as e:
            print(f"\n❌ Setup failed: {e}")
            sys.exit(1)
    
    def _install_dependencies(self) -> None:
        """Install required dependencies."""
        print("\n📦 Installing dependencies...")
        
        # Phase 2 dependencies
        phase2_deps = [
            # Observability
            "opentelemetry-api>=1.20.0",
            "opentelemetry-sdk>=1.20.0",
            "opentelemetry-exporter-jaeger>=1.20.0",
            "opentelemetry-exporter-prometheus>=1.12.0",
            "opentelemetry-instrumentation-asyncio>=0.41b0",
            "opentelemetry-instrumentation-logging>=0.41b0",
            "prometheus-client>=0.17.0",
            
            # Configuration management
            "watchdog>=3.0.0",
            "pydantic>=2.0.0",
            "pydantic-settings>=2.0.0",
            
            # Resilience patterns
            "tenacity>=8.2.0",
            
            # Plugin system
            "packaging>=23.0",
            
            # Health monitoring
            "psutil>=5.9.0",
            
            # Web framework (optional)
            "fastapi>=0.100.0",
            "uvicorn>=0.23.0",
            
            # HTTP client
            "aiohttp>=3.8.0",
        ]
        
        # Update requirements.txt
        requirements_file = self.project_root / "requirements.txt"
        
        # Read existing requirements
        existing_deps = set()
        if requirements_file.exists():
            with open(requirements_file, 'r') as f:
                existing_deps = {line.strip() for line in f if line.strip() and not line.startswith('#')}
        
        # Add new dependencies
        all_deps = existing_deps.union(set(phase2_deps))
        
        # Write updated requirements
        with open(requirements_file, 'w') as f:
            f.write("# Agent Framework Dependencies\n")
            f.write("# Phase 1: Core Framework\n")
            for dep in sorted(existing_deps):
                if dep not in phase2_deps:
                    f.write(f"{dep}\n")
            
            f.write("\n# Phase 2: Architecture Improvements & Observability\n")
            for dep in sorted(phase2_deps):
                f.write(f"{dep}\n")
        
        # Install dependencies
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True, capture_output=True, text=True)
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e.stderr}")
            raise
    
    def _create_configurations(self) -> None:
        """Create configuration files."""
        print("\n⚙️ Creating configuration files...")
        
        # Create config directories
        self.config_dir.mkdir(exist_ok=True)
        (self.config_dir / "observability").mkdir(exist_ok=True)
        (self.config_dir / "security").mkdir(exist_ok=True)
        (self.config_dir / "plugins").mkdir(exist_ok=True)
        
        # Observability configuration
        observability_config = {
            "service_name": "agent-framework",
            "service_version": "2.0.0",
            "environment": "development",
            "telemetry": {
                "enable_tracing": True,
                "enable_metrics": True,
                "jaeger_endpoint": "http://localhost:14268/api/traces",
                "prometheus_port": 8000,
                "trace_sample_rate": 1.0
            },
            "metrics": {
                "collection_interval": 30,
                "export_interval": 30,
                "enable_system_metrics": True
            },
            "health": {
                "check_interval": 30,
                "history_size": 100,
                "thresholds": {
                    "cpu_percent_warning": 80.0,
                    "cpu_percent_critical": 95.0,
                    "memory_percent_warning": 80.0,
                    "memory_percent_critical": 95.0,
                    "disk_percent_warning": 80.0,
                    "disk_percent_critical": 95.0
                }
            }
        }
        
        with open(self.config_dir / "observability" / "config.yaml", 'w') as f:
            yaml.dump(observability_config, f, default_flow_style=False)
        
        # Security configuration
        security_config = {
            "input_validation": {
                "enable_sql_injection_detection": True,
                "enable_xss_protection": True,
                "enable_command_injection_protection": True,
                "max_input_length": 10000,
                "max_json_depth": 10
            },
            "secrets_management": {
                "encryption_algorithm": "AES-256",
                "key_rotation_interval": 86400,  # 24 hours
                "secret_expiration": 2592000,    # 30 days
                "audit_enabled": True
            },
            "monitoring": {
                "enable_threat_detection": True,
                "rate_limiting": {
                    "requests_per_minute": 100,
                    "burst_size": 20
                },
                "anomaly_detection": {
                    "cpu_threshold": 90.0,
                    "memory_threshold": 90.0,
                    "disk_threshold": 95.0
                }
            }
        }
        
        with open(self.config_dir / "security" / "config.yaml", 'w') as f:
            yaml.dump(security_config, f, default_flow_style=False)
        
        # Plugin system configuration
        plugin_config = {
            "plugin_dirs": ["plugins", "custom_plugins"],
            "auto_reload": True,
            "dependency_resolution": True,
            "communication_bus": {
                "enable": True,
                "message_queue_size": 1000,
                "timeout": 30.0
            },
            "sandboxing": {
                "enable": False,  # Disabled by default
                "resource_limits": {
                    "max_memory_mb": 512,
                    "max_cpu_percent": 50.0,
                    "max_execution_time": 300.0
                }
            }
        }
        
        with open(self.config_dir / "plugins" / "config.yaml", 'w') as f:
            yaml.dump(plugin_config, f, default_flow_style=False)
        
        print("✅ Configuration files created")
    
    def _setup_observability(self) -> None:
        """Set up observability components."""
        print("\n📊 Setting up observability...")
        
        # Create Prometheus configuration
        prometheus_config = {
            "global": {
                "scrape_interval": "15s",
                "evaluation_interval": "15s"
            },
            "scrape_configs": [
                {
                    "job_name": "agent-framework",
                    "static_configs": [
                        {"targets": ["localhost:8000"]}
                    ],
                    "scrape_interval": "5s",
                    "metrics_path": "/metrics"
                }
            ]
        }
        
        prometheus_dir = self.config_dir / "prometheus"
        prometheus_dir.mkdir(exist_ok=True)
        
        with open(prometheus_dir / "prometheus.yml", 'w') as f:
            yaml.dump(prometheus_config, f, default_flow_style=False)
        
        # Create Docker Compose for observability stack
        docker_compose = {
            "version": "3.8",
            "services": {
                "prometheus": {
                    "image": "prom/prometheus:latest",
                    "ports": ["9090:9090"],
                    "volumes": [
                        "./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml"
                    ],
                    "command": [
                        "--config.file=/etc/prometheus/prometheus.yml",
                        "--storage.tsdb.path=/prometheus",
                        "--web.console.libraries=/etc/prometheus/console_libraries",
                        "--web.console.templates=/etc/prometheus/consoles",
                        "--web.enable-lifecycle"
                    ]
                },
                "grafana": {
                    "image": "grafana/grafana:latest",
                    "ports": ["3000:3000"],
                    "environment": [
                        "GF_SECURITY_ADMIN_PASSWORD=admin"
                    ],
                    "volumes": [
                        "grafana-storage:/var/lib/grafana",
                        "./dashboards:/var/lib/grafana/dashboards"
                    ]
                },
                "jaeger": {
                    "image": "jaegertracing/all-in-one:latest",
                    "ports": [
                        "16686:16686",
                        "14268:14268"
                    ],
                    "environment": [
                        "COLLECTOR_OTLP_ENABLED=true"
                    ]
                }
            },
            "volumes": {
                "grafana-storage": {}
            }
        }
        
        with open(self.project_root / "docker-compose.observability.yml", 'w') as f:
            yaml.dump(docker_compose, f, default_flow_style=False)
        
        print("✅ Observability setup completed")
    
    def _generate_dashboards(self) -> None:
        """Generate monitoring dashboards."""
        print("\n📈 Generating monitoring dashboards...")
        
        # Create dashboards directory
        self.dashboards_dir.mkdir(exist_ok=True)
        
        # Generate dashboards using the dashboard generator
        try:
            # Import and use the dashboard generator
            sys.path.insert(0, str(self.project_root / "src"))
            from agent_framework.observability.dashboard_config import DashboardGenerator
            
            generator = DashboardGenerator()
            exported = generator.export_all_dashboards(str(self.dashboards_dir))
            
            print(f"✅ Generated {len(exported)} dashboards:")
            for name, path in exported.items():
                print(f"  - {name}: {path}")
                
        except ImportError as e:
            print(f"⚠️ Could not generate dashboards: {e}")
            print("   Dashboards will be generated when the framework is first run")
    
    def _create_examples(self) -> None:
        """Create example configurations and usage scripts."""
        print("\n📝 Creating examples...")
        
        examples_dir = self.project_root / "examples" / "phase2"
        examples_dir.mkdir(parents=True, exist_ok=True)
        
        # Example observability usage
        observability_example = '''#!/usr/bin/env python3
"""
Example: Using the enhanced observability features.
"""

import asyncio
from agent_framework.observability import (
    initialize_observability, 
    ObservabilityConfig,
    trace_function,
    time_operation,
    record_metric
)

@trace_function("example_operation")
@time_operation("example_timing")
async def example_operation(data: str) -> str:
    """Example operation with tracing and timing."""
    # Simulate some work
    await asyncio.sleep(0.1)
    
    # Record custom metrics
    record_metric("example_operations_total", 1, {"operation": "process"})
    
    return f"Processed: {data}"

async def main():
    """Main example function."""
    # Initialize observability
    config = ObservabilityConfig(
        service_name="example-service",
        environment="development"
    )
    
    obs_manager = await initialize_observability(config)
    
    try:
        # Use traced operations
        result = await example_operation("test data")
        print(f"Result: {result}")
        
        # Get health status
        health = await obs_manager.get_health_status()
        print(f"Health: {health['status']}")
        
    finally:
        # Cleanup
        await obs_manager.stop()

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        with open(examples_dir / "observability_example.py", 'w') as f:
            f.write(observability_example)
        
        # Example plugin
        plugin_example = '''"""
Example plugin using the enhanced plugin system.
"""

from agent_framework.plugins.enhanced_plugin_system import (
    BasePlugin, PluginMetadata, PluginVersion, PluginDependency, MessageType
)

class ExamplePlugin(BasePlugin):
    """Example plugin demonstrating enhanced features."""
    
    @classmethod
    def get_metadata(cls) -> PluginMetadata:
        """Get plugin metadata."""
        return PluginMetadata(
            name="example_plugin",
            version=PluginVersion(1, 0, 0),
            description="Example plugin for demonstration",
            author="Agent Framework Team",
            dependencies=[
                PluginDependency(
                    name="base_plugin",
                    min_version=PluginVersion(1, 0, 0),
                    optional=True
                )
            ],
            provides=["example_capability"],
            requires=["logging"],
            tags=["example", "demo"],
            hot_reload=True,
            priority=100
        )
    
    async def initialize(self) -> None:
        """Initialize the plugin."""
        self.logger.info("Example plugin initializing...")
        
        # Register message handlers
        self.register_message_handler("example.request", self._handle_request)
        self.register_message_handler("example.event", self._handle_event)
    
    async def activate(self) -> None:
        """Activate the plugin."""
        self.logger.info("Example plugin activated")
        
        # Send activation event
        await self.send_message(
            recipient=None,  # Broadcast
            topic="plugin.activated",
            payload={"plugin": self.get_metadata().name},
            message_type=MessageType.EVENT
        )
    
    async def _handle_request(self, message):
        """Handle example request."""
        self.logger.info(f"Handling request: {message.payload}")
        
        # Process request and send response
        response_payload = {"result": f"Processed: {message.payload}"}
        
        await self.send_message(
            recipient=message.sender,
            topic="example.response",
            payload=response_payload,
            message_type=MessageType.RESPONSE
        )
    
    async def _handle_event(self, message):
        """Handle example event."""
        self.logger.info(f"Received event: {message.payload}")
'''
        
        with open(examples_dir / "example_plugin.py", 'w') as f:
            f.write(plugin_example)
        
        print("✅ Examples created")
    
    def _run_validation(self) -> None:
        """Run validation tests."""
        print("\n🧪 Running validation tests...")
        
        try:
            # Test imports
            sys.path.insert(0, str(self.project_root / "src"))
            
            # Test core imports
            from agent_framework.observability import ObservabilityManager
            from agent_framework.security import SecurityManager
            from agent_framework.resilience.circuit_breaker import CircuitBreaker
            from agent_framework.shared.ast_analysis_engine import ast_analysis_engine
            from agent_framework.shared.error_handling import ErrorHandler
            
            print("✅ All imports successful")
            
            # Test basic functionality
            obs_manager = ObservabilityManager()
            print("✅ ObservabilityManager instantiated")
            
            security_manager = SecurityManager()
            print("✅ SecurityManager instantiated")
            
            print("✅ Validation completed successfully")
            
        except Exception as e:
            print(f"❌ Validation failed: {e}")
            raise
    
    def _print_next_steps(self) -> None:
        """Print next steps for the user."""
        print("\n🎉 Phase 2 Setup Complete!")
        print("\n📋 Next Steps:")
        print("1. Start the observability stack:")
        print("   docker-compose -f docker-compose.observability.yml up -d")
        print("\n2. Access monitoring dashboards:")
        print("   - Prometheus: http://localhost:9090")
        print("   - Grafana: http://localhost:3000 (admin/admin)")
        print("   - Jaeger: http://localhost:16686")
        print("\n3. Import Grafana dashboards from ./dashboards/")
        print("\n4. Run example code:")
        print("   python examples/phase2/observability_example.py")
        print("\n5. Review configuration files in ./config/")
        print("\n📚 Documentation:")
        print("   - Observability: docs/observability.md")
        print("   - Security: docs/security_enhancements.md")
        print("   - Plugin System: docs/plugin_system.md")


if __name__ == "__main__":
    setup_manager = Phase2SetupManager()
    setup_manager.run_setup()
