"""
Enhanced plugin system with dependency resolution, versioning, and communication.

Provides advanced plugin management capabilities including dependency graphs,
version compatibility, hot-reload, and inter-plugin communication.
"""

import asyncio
import logging
import importlib
import inspect
from typing import Dict, List, Any, Optional, Set, Callable, Type, Union
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from enum import Enum
import json
import hashlib

try:
    from packaging import version
    PACKAGING_AVAILABLE = True
except ImportError:
    PACKAGING_AVAILABLE = False


class PluginState(Enum):
    """Plugin states."""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"


class MessageType(Enum):
    """Inter-plugin message types."""
    REQUEST = "request"
    RESPONSE = "response"
    EVENT = "event"
    BROADCAST = "broadcast"


@dataclass
class PluginVersion:
    """Plugin version information."""
    major: int
    minor: int
    patch: int
    pre_release: Optional[str] = None
    
    def __str__(self) -> str:
        version_str = f"{self.major}.{self.minor}.{self.patch}"
        if self.pre_release:
            version_str += f"-{self.pre_release}"
        return version_str
    
    def __lt__(self, other: 'PluginVersion') -> bool:
        return (self.major, self.minor, self.patch) < (other.major, other.minor, other.patch)
    
    def __eq__(self, other: 'PluginVersion') -> bool:
        return (self.major, self.minor, self.patch) == (other.major, other.minor, other.patch)
    
    @classmethod
    def from_string(cls, version_str: str) -> 'PluginVersion':
        """Parse version from string."""
        parts = version_str.split('-', 1)
        version_part = parts[0]
        pre_release = parts[1] if len(parts) > 1 else None
        
        major, minor, patch = map(int, version_part.split('.'))
        return cls(major, minor, patch, pre_release)


@dataclass
class PluginDependency:
    """Plugin dependency specification."""
    name: str
    min_version: Optional[PluginVersion] = None
    max_version: Optional[PluginVersion] = None
    optional: bool = False
    
    def is_satisfied_by(self, version: PluginVersion) -> bool:
        """Check if a version satisfies this dependency."""
        if self.min_version and version < self.min_version:
            return False
        if self.max_version and version > self.max_version:
            return False
        return True


@dataclass
class PluginMetadata:
    """Plugin metadata."""
    name: str
    version: PluginVersion
    description: str = ""
    author: str = ""
    dependencies: List[PluginDependency] = field(default_factory=list)
    provides: List[str] = field(default_factory=list)  # Capabilities provided
    requires: List[str] = field(default_factory=list)  # Capabilities required
    tags: List[str] = field(default_factory=list)
    hot_reload: bool = True
    priority: int = 100  # Loading priority (lower = higher priority)


@dataclass
class PluginMessage:
    """Inter-plugin message."""
    message_id: str
    sender: str
    recipient: Optional[str]  # None for broadcast
    message_type: MessageType
    topic: str
    payload: Any
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None


class BasePlugin:
    """Base class for all plugins."""
    
    def __init__(self, plugin_manager: 'EnhancedPluginManager'):
        """Initialize the plugin."""
        self.plugin_manager = plugin_manager
        self.logger = logging.getLogger(f"{__name__}.{self.get_metadata().name}")
        self._state = PluginState.UNLOADED
        self._message_handlers: Dict[str, Callable] = {}
    
    @classmethod
    def get_metadata(cls) -> PluginMetadata:
        """Get plugin metadata. Must be implemented by subclasses."""
        raise NotImplementedError("Plugins must implement get_metadata()")
    
    async def initialize(self) -> None:
        """Initialize the plugin. Override in subclasses."""
        pass
    
    async def activate(self) -> None:
        """Activate the plugin. Override in subclasses."""
        pass
    
    async def deactivate(self) -> None:
        """Deactivate the plugin. Override in subclasses."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup plugin resources. Override in subclasses."""
        pass
    
    def register_message_handler(self, topic: str, handler: Callable) -> None:
        """Register a message handler for a topic."""
        self._message_handlers[topic] = handler
    
    async def handle_message(self, message: PluginMessage) -> Optional[Any]:
        """Handle an incoming message."""
        if message.topic in self._message_handlers:
            try:
                handler = self._message_handlers[message.topic]
                if asyncio.iscoroutinefunction(handler):
                    return await handler(message)
                else:
                    return handler(message)
            except Exception as e:
                self.logger.error(f"Error handling message {message.topic}: {e}")
        return None
    
    async def send_message(self, 
                          recipient: Optional[str],
                          topic: str,
                          payload: Any,
                          message_type: MessageType = MessageType.EVENT) -> str:
        """Send a message to another plugin or broadcast."""
        return await self.plugin_manager.send_message(
            sender=self.get_metadata().name,
            recipient=recipient,
            topic=topic,
            payload=payload,
            message_type=message_type
        )
    
    @property
    def state(self) -> PluginState:
        """Get plugin state."""
        return self._state


class PluginCommunicationBus:
    """Communication bus for inter-plugin messaging."""
    
    def __init__(self):
        """Initialize the communication bus."""
        self.logger = logging.getLogger(__name__)
        self._subscribers: Dict[str, List[str]] = {}  # topic -> plugin names
        self._message_queue: asyncio.Queue = asyncio.Queue()
        self._running = False
        self._processor_task: Optional[asyncio.Task] = None
    
    async def start(self) -> None:
        """Start the communication bus."""
        if self._running:
            return
        
        self._running = True
        self._processor_task = asyncio.create_task(self._process_messages())
        self.logger.info("Plugin communication bus started")
    
    async def stop(self) -> None:
        """Stop the communication bus."""
        if not self._running:
            return
        
        self._running = False
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Plugin communication bus stopped")
    
    def subscribe(self, plugin_name: str, topic: str) -> None:
        """Subscribe a plugin to a topic."""
        if topic not in self._subscribers:
            self._subscribers[topic] = []
        
        if plugin_name not in self._subscribers[topic]:
            self._subscribers[topic].append(plugin_name)
            self.logger.debug(f"Plugin {plugin_name} subscribed to topic {topic}")
    
    def unsubscribe(self, plugin_name: str, topic: str) -> None:
        """Unsubscribe a plugin from a topic."""
        if topic in self._subscribers and plugin_name in self._subscribers[topic]:
            self._subscribers[topic].remove(plugin_name)
            self.logger.debug(f"Plugin {plugin_name} unsubscribed from topic {topic}")
    
    async def send_message(self, message: PluginMessage) -> None:
        """Send a message through the bus."""
        await self._message_queue.put(message)
    
    async def _process_messages(self) -> None:
        """Process messages in the queue."""
        while self._running:
            try:
                message = await asyncio.wait_for(
                    self._message_queue.get(),
                    timeout=1.0
                )
                await self._deliver_message(message)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
    
    async def _deliver_message(self, message: PluginMessage) -> None:
        """Deliver a message to recipients."""
        # Import here to avoid circular imports
        from .enhanced_plugin_system import EnhancedPluginManager
        
        plugin_manager = EnhancedPluginManager._instance
        if not plugin_manager:
            return
        
        if message.recipient:
            # Direct message
            await plugin_manager._deliver_message_to_plugin(message.recipient, message)
        else:
            # Broadcast to subscribers
            subscribers = self._subscribers.get(message.topic, [])
            for subscriber in subscribers:
                if subscriber != message.sender:  # Don't send to sender
                    await plugin_manager._deliver_message_to_plugin(subscriber, message)


class DependencyResolver:
    """Resolves plugin dependencies and determines loading order."""
    
    def __init__(self):
        """Initialize the dependency resolver."""
        self.logger = logging.getLogger(__name__)
    
    def resolve_dependencies(self, 
                           plugins: Dict[str, PluginMetadata]) -> List[str]:
        """
        Resolve plugin dependencies and return loading order.
        
        Args:
            plugins: Dictionary of plugin name to metadata
            
        Returns:
            List of plugin names in loading order
            
        Raises:
            ValueError: If dependencies cannot be resolved
        """
        # Build dependency graph
        graph = {}
        in_degree = {}
        
        for name, metadata in plugins.items():
            graph[name] = []
            in_degree[name] = 0
        
        # Add edges for dependencies
        for name, metadata in plugins.items():
            for dep in metadata.dependencies:
                if dep.name in plugins:
                    # Check version compatibility
                    dep_metadata = plugins[dep.name]
                    if not dep.is_satisfied_by(dep_metadata.version):
                        if not dep.optional:
                            raise ValueError(
                                f"Plugin {name} requires {dep.name} "
                                f"version {dep.min_version}-{dep.max_version}, "
                                f"but found {dep_metadata.version}"
                            )
                        continue
                    
                    graph[dep.name].append(name)
                    in_degree[name] += 1
                elif not dep.optional:
                    raise ValueError(f"Required dependency {dep.name} not found for plugin {name}")
        
        # Topological sort with priority
        result = []
        queue = []
        
        # Start with plugins that have no dependencies
        for name in plugins:
            if in_degree[name] == 0:
                queue.append((plugins[name].priority, name))
        
        queue.sort()  # Sort by priority
        
        while queue:
            _, current = queue.pop(0)
            result.append(current)
            
            # Update in-degrees of dependent plugins
            for dependent in graph[current]:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append((plugins[dependent].priority, dependent))
                    queue.sort()
        
        # Check for circular dependencies
        if len(result) != len(plugins):
            remaining = [name for name in plugins if name not in result]
            raise ValueError(f"Circular dependency detected among plugins: {remaining}")
        
        return result
    
    def check_capability_requirements(self, 
                                    plugins: Dict[str, PluginMetadata]) -> Dict[str, List[str]]:
        """
        Check if all capability requirements are satisfied.
        
        Returns:
            Dictionary of unsatisfied requirements by plugin
        """
        # Build capability map
        provided_capabilities = set()
        for metadata in plugins.values():
            provided_capabilities.update(metadata.provides)
        
        # Check requirements
        unsatisfied = {}
        for name, metadata in plugins.items():
            missing = []
            for required in metadata.requires:
                if required not in provided_capabilities:
                    missing.append(required)
            
            if missing:
                unsatisfied[name] = missing
        
        return unsatisfied


class EnhancedPluginManager:
    """
    Enhanced plugin manager with advanced features.
    
    Features:
    - Dependency resolution and loading order
    - Version compatibility checking
    - Hot-reload capabilities
    - Inter-plugin communication bus
    - Plugin sandboxing and isolation
    - Comprehensive monitoring and metrics
    """
    
    _instance: Optional['EnhancedPluginManager'] = None
    
    def __init__(self, plugin_dirs: List[Path] = None):
        """Initialize the enhanced plugin manager."""
        if EnhancedPluginManager._instance is None:
            EnhancedPluginManager._instance = self
        
        self.logger = logging.getLogger(__name__)
        self.plugin_dirs = plugin_dirs or [Path("plugins")]
        
        # Plugin registry
        self._plugins: Dict[str, BasePlugin] = {}
        self._plugin_metadata: Dict[str, PluginMetadata] = {}
        self._plugin_modules: Dict[str, Any] = {}
        
        # Dependency management
        self._dependency_resolver = DependencyResolver()
        self._loading_order: List[str] = []
        
        # Communication
        self._communication_bus = PluginCommunicationBus()
        
        # Hot-reload tracking
        self._file_checksums: Dict[Path, str] = {}
        self._watch_task: Optional[asyncio.Task] = None
        
        # State
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the plugin manager."""
        if self._initialized:
            return
        
        # Start communication bus
        await self._communication_bus.start()
        
        # Discover and load plugins
        await self.discover_plugins()
        await self.load_all_plugins()
        
        self._initialized = True
        self.logger.info("Enhanced plugin manager initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the plugin manager."""
        if not self._initialized:
            return
        
        # Stop hot-reload watching
        if self._watch_task:
            self._watch_task.cancel()
        
        # Deactivate and cleanup all plugins
        for plugin_name in reversed(self._loading_order):
            await self._deactivate_plugin(plugin_name)
            await self._cleanup_plugin(plugin_name)
        
        # Stop communication bus
        await self._communication_bus.stop()
        
        self._initialized = False
        self.logger.info("Enhanced plugin manager shutdown")
    
    async def discover_plugins(self) -> None:
        """Discover available plugins."""
        discovered = {}
        
        for plugin_dir in self.plugin_dirs:
            if not plugin_dir.exists():
                continue
            
            for plugin_file in plugin_dir.glob("*.py"):
                if plugin_file.name.startswith("__"):
                    continue
                
                try:
                    metadata = await self._extract_plugin_metadata(plugin_file)
                    if metadata:
                        discovered[metadata.name] = metadata
                        self.logger.info(f"Discovered plugin: {metadata.name} v{metadata.version}")
                except Exception as e:
                    self.logger.error(f"Failed to discover plugin {plugin_file}: {e}")
        
        self._plugin_metadata = discovered
        
        # Resolve dependencies and determine loading order
        try:
            self._loading_order = self._dependency_resolver.resolve_dependencies(discovered)
            self.logger.info(f"Plugin loading order: {self._loading_order}")
        except ValueError as e:
            self.logger.error(f"Dependency resolution failed: {e}")
            raise
        
        # Check capability requirements
        unsatisfied = self._dependency_resolver.check_capability_requirements(discovered)
        if unsatisfied:
            self.logger.warning(f"Unsatisfied capability requirements: {unsatisfied}")
    
    async def load_all_plugins(self) -> None:
        """Load all discovered plugins in dependency order."""
        for plugin_name in self._loading_order:
            try:
                await self._load_plugin(plugin_name)
                await self._activate_plugin(plugin_name)
            except Exception as e:
                self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
    
    async def _extract_plugin_metadata(self, plugin_file: Path) -> Optional[PluginMetadata]:
        """Extract metadata from a plugin file."""
        # This is a simplified implementation
        # In practice, you might want to use AST parsing or import the module safely
        try:
            spec = importlib.util.spec_from_file_location("temp_plugin", plugin_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Look for plugin class
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, BasePlugin) and 
                    obj != BasePlugin):
                    return obj.get_metadata()
            
        except Exception as e:
            self.logger.error(f"Failed to extract metadata from {plugin_file}: {e}")
        
        return None
    
    async def _load_plugin(self, plugin_name: str) -> None:
        """Load a specific plugin."""
        # Implementation would load the plugin module and instantiate the plugin class
        # This is a simplified version
        pass
    
    async def _activate_plugin(self, plugin_name: str) -> None:
        """Activate a specific plugin."""
        if plugin_name in self._plugins:
            plugin = self._plugins[plugin_name]
            await plugin.activate()
            plugin._state = PluginState.ACTIVE
            self.logger.info(f"Activated plugin: {plugin_name}")
    
    async def _deactivate_plugin(self, plugin_name: str) -> None:
        """Deactivate a specific plugin."""
        if plugin_name in self._plugins:
            plugin = self._plugins[plugin_name]
            await plugin.deactivate()
            plugin._state = PluginState.LOADED
            self.logger.info(f"Deactivated plugin: {plugin_name}")
    
    async def _cleanup_plugin(self, plugin_name: str) -> None:
        """Cleanup a specific plugin."""
        if plugin_name in self._plugins:
            plugin = self._plugins[plugin_name]
            await plugin.cleanup()
            plugin._state = PluginState.UNLOADED
            del self._plugins[plugin_name]
            self.logger.info(f"Cleaned up plugin: {plugin_name}")
    
    async def send_message(self,
                          sender: str,
                          recipient: Optional[str],
                          topic: str,
                          payload: Any,
                          message_type: MessageType = MessageType.EVENT) -> str:
        """Send a message through the communication bus."""
        message_id = hashlib.md5(f"{sender}{topic}{datetime.now()}".encode()).hexdigest()[:8]
        
        message = PluginMessage(
            message_id=message_id,
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            topic=topic,
            payload=payload
        )
        
        await self._communication_bus.send_message(message)
        return message_id
    
    async def _deliver_message_to_plugin(self, plugin_name: str, message: PluginMessage) -> None:
        """Deliver a message to a specific plugin."""
        if plugin_name in self._plugins:
            plugin = self._plugins[plugin_name]
            try:
                await plugin.handle_message(message)
            except Exception as e:
                self.logger.error(f"Error delivering message to {plugin_name}: {e}")
    
    def get_plugin_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all plugins."""
        info = {}
        for name, plugin in self._plugins.items():
            metadata = self._plugin_metadata.get(name)
            info[name] = {
                "state": plugin.state.value,
                "version": str(metadata.version) if metadata else "unknown",
                "description": metadata.description if metadata else "",
                "dependencies": [dep.name for dep in metadata.dependencies] if metadata else []
            }
        return info
